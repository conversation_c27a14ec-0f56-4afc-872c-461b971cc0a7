#!/bin/bash

# Script para build local da imagem Docker
# Uso: ./build-local.sh [GITHUB_TOKEN]

PROJECT_ID="highcapital-470117"
IMAGE_NAME="authentication-service"
GITHUB_USERNAME="aryelzx"

# Verificar se o token foi fornecido
# if [ -z "$1" ]; then
#     echo "Uso: ./build-local.sh <GITHUB_TOKEN>"
#     echo "Exemplo: ./build-local.sh ****************************************"
#     exit 1
# fi

GITHUB_TOKEN="****************************************"

echo "🔨 Fazendo build da imagem Docker..."
docker build -t "gcr.io/highcapital-470117/authentication-service:latest" --build-arg GITHUB_TOKEN="****************************************" --build-arg GITHUB_USERNAME="aryelzx" .

if [ $? -eq 0 ]; then
    echo "✅ Build concluído com sucesso!"
    echo "📦 Imagem: gcr.io/$PROJECT_ID/$IMAGE_NAME:latest"
    echo ""
    echo "Para testar localmente:"
    echo "docker run -p 8080:8080 gcr.io/$PROJECT_ID/$IMAGE_NAME:latest"
    echo ""
    echo "Para fazer push:"
    echo "docker push gcr.io/$PROJECT_ID/$IMAGE_NAME:latest"
else
    echo "❌ Erro no build!"
    exit 1
fi

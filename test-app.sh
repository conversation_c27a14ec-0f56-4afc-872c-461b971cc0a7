#!/bin/bash

echo "🧪 Testando a aplicação localmente..."
echo ""

# Build da aplicação
echo "🔨 Fazendo build da aplicação..."
dotnet build HighCapitalAuthenticationService.sln

if [ $? -ne 0 ]; then
    echo "❌ Erro no build da aplicação"
    exit 1
fi

echo "✅ Build concluído com sucesso!"
echo ""

# Build da imagem Docker
echo "🐳 Fazendo build da imagem Docker..."
docker build -t authentication-service-test \
    --build-arg GITHUB_TOKEN="****************************************" \
    --build-arg GITHUB_USERNAME="aryelzx" \
    .

if [ $? -ne 0 ]; then
    echo "❌ Erro no build da imagem Docker"
    exit 1
fi

echo "✅ Imagem Docker criada com sucesso!"
echo ""

# Testar a imagem
echo "🚀 Testando a imagem Docker..."
echo "Iniciando container na porta 8080..."
echo "Pressione Ctrl+C para parar"
echo ""

docker run -p 8080:8080 \
    -e ASPNETCORE_ENVIRONMENT=Development \
    -e ConnectionStrings__DefaultConnection="Server=host.docker.internal;Database=postgres;Uid=high-capital-dev;Pwd=****************" \
    authentication-service-test

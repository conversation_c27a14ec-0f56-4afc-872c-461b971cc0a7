#!/bin/bash

echo "🚀 Upgrade do cluster para resolver problemas de recursos..."
echo ""

PROJECT_ID="highcapital-470117"
CLUSTER_NAME="cluster-high-capital-dev"
ZONE="us-east1-b"

# Função para verificar se comando existe
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

if ! command_exists gcloud; then
    echo "❌ gcloud CLI não encontrado. Instale primeiro."
    exit 1
fi

echo "📊 Status atual do cluster:"
gcloud container clusters describe $CLUSTER_NAME --zone=$ZONE --format="table(name,status,currentNodeCount,location)"
echo ""

echo "🔍 Node pools atuais:"
gcloud container node-pools list --cluster=$CLUSTER_NAME --zone=$ZONE
echo ""

echo "💡 Opções de upgrade:"
echo "1. Criar novo node pool com máquinas maiores (recomendado)"
echo "2. Escalar node pool atual"
echo "3. Cancelar"
echo ""

read -p "Escolha uma opção (1-3): " choice

case $choice in
    1)
        echo "🏗️  Criando novo node pool com e2-standard-2..."
        
        # Criar novo node pool com máquinas maiores
        gcloud container node-pools create larger-pool \
            --cluster=$CLUSTER_NAME \
            --zone=$ZONE \
            --machine-type=e2-standard-2 \
            --num-nodes=2 \
            --enable-autoscaling \
            --min-nodes=1 \
            --max-nodes=4 \
            --enable-autorepair \
            --enable-autoupgrade \
            --disk-size=20GB \
            --disk-type=pd-standard
        
        if [ $? -eq 0 ]; then
            echo "✅ Novo node pool criado com sucesso!"
            echo ""
            echo "🔄 Aplicando deployment com prioridade alta..."
            kubectl apply -f k8s/priority-class.yaml
            
            echo "⏳ Aguardando pods ficarem prontos..."
            sleep 60
            
            echo "📊 Status dos pods:"
            kubectl get pods -o wide
            
            echo ""
            echo "🗑️  Para remover o node pool antigo (após confirmar que tudo funciona):"
            echo "gcloud container node-pools delete default-pool --cluster=$CLUSTER_NAME --zone=$ZONE"
        else
            echo "❌ Falha ao criar node pool"
        fi
        ;;
    2)
        echo "📈 Escalando node pool atual para 3 nodes..."
        gcloud container clusters resize $CLUSTER_NAME --num-nodes=3 --zone=$ZONE
        
        if [ $? -eq 0 ]; then
            echo "✅ Cluster escalado com sucesso!"
            echo "🔄 Aplicando deployment..."
            kubectl apply -f k8s/deployment.yaml
        else
            echo "❌ Falha ao escalar cluster"
        fi
        ;;
    3)
        echo "❌ Operação cancelada"
        exit 0
        ;;
    *)
        echo "❌ Opção inválida"
        exit 1
        ;;
esac

echo ""
echo "📊 Status final:"
kubectl get nodes
kubectl get pods -o wide
echo ""

echo "🌐 Para acessar o serviço:"
kubectl get service authentication-service

#!/bin/bash

echo "🔧 Resolvendo problema de preempção..."
echo ""

# Função para verificar se comando existe
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

echo "📊 Status atual do cluster:"
kubectl get nodes -o wide
echo ""

echo "💾 Uso de recursos por node:"
kubectl describe nodes | grep -A 5 "Allocated resources"
echo ""

echo "🏃 Pods em execução:"
kubectl get pods -o wide --all-namespaces
echo ""

echo "⚠️  Pods pendentes:"
kubectl get pods --field-selector=status.phase=Pending -o wide
echo ""

echo "🧹 Limpando pods desnecessários..."
# Limpar pods completed/failed
kubectl delete pods --field-selector=status.phase=Succeeded --all-namespaces 2>/dev/null || true
kubectl delete pods --field-selector=status.phase=Failed --all-namespaces 2>/dev/null || true
echo ""

echo "🔄 Tentando escalar o cluster..."
if command_exists gcloud; then
    # Primeiro, verificar configuração atual do node pool
    echo "📋 Configuração atual do node pool:"
    gcloud container node-pools describe default-pool \
        --cluster=cluster-high-capital-dev \
        --zone=us-east1 2>/dev/null || echo "⚠️  Não foi possível obter configuração do node pool"
    echo ""
    
    # Tentar escalar para mais nodes
    echo "📈 Escalando para 3 nodes..."
    gcloud container clusters resize cluster-high-capital-dev \
        --num-nodes=3 \
        --zone=us-east1 \
        --quiet 2>/dev/null || echo "❌ Falha ao escalar cluster"
    
    # Aguardar nodes ficarem prontos
    echo "⏳ Aguardando nodes ficarem prontos..."
    sleep 60
    
    echo "📊 Status após escalar:"
    kubectl get nodes
    echo ""
else
    echo "⚠️  gcloud CLI não encontrado. Execute manualmente:"
    echo "gcloud container clusters resize cluster-high-capital-dev --num-nodes=3 --zone=us-east1"
fi

echo "🔄 Tentando aplicar deployment novamente..."
kubectl apply -f k8s/deployment.yaml
echo ""

echo "⏳ Aguardando 30 segundos..."
sleep 30

echo "📊 Status final:"
kubectl get pods -o wide
echo ""

# Verificar se ainda há problemas
PENDING_PODS=$(kubectl get pods --field-selector=status.phase=Pending --no-headers 2>/dev/null | wc -l)
if [ "$PENDING_PODS" -gt 0 ]; then
    echo "⚠️  Ainda há pods pendentes. Sugestões:"
    echo ""
    echo "1. 🔧 Usar configuração com menos recursos:"
    echo "   kubectl apply -f k8s/deployment-minimal.yaml"
    echo ""
    echo "2. 🚀 Upgrade do cluster para nodes maiores:"
    echo "   gcloud container node-pools create larger-pool \\"
    echo "     --cluster=cluster-high-capital-dev \\"
    echo "     --zone=us-east1 \\"
    echo "     --machine-type=e2-standard-2 \\"
    echo "     --num-nodes=2"
    echo ""
    echo "3. 📊 Verificar detalhes do problema:"
    echo "   kubectl describe pods | grep -A 10 'Events:'"
else
    echo "✅ Problema resolvido! Pods estão rodando."
    echo ""
    echo "🌐 Para acessar o serviço:"
    kubectl get service authentication-service
fi

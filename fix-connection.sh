#!/bin/bash

echo "🔧 Resolvendo problemas de conexão com o cluster..."
echo ""

PROJECT_ID="highcapital-470117"
CLUSTER_NAME="cluster-high-capital-dev"
ZONE="us-east1-b"

# Função para verificar se comando existe
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

if ! command_exists gcloud; then
    echo "❌ gcloud CLI não encontrado. Instale primeiro."
    exit 1
fi

echo "🔍 Verificando configuração atual do gcloud..."
gcloud config list
echo ""

echo "🔑 Verificando autenticação..."
gcloud auth list
echo ""

echo "📊 Verificando se o cluster existe..."
gcloud container clusters list --filter="name:$CLUSTER_NAME"
echo ""

echo "🔄 Reconfigurando credenciais do cluster..."
gcloud container clusters get-credentials $CLUSTER_NAME --zone=$ZONE --project=$PROJECT_ID

if [ $? -eq 0 ]; then
    echo "✅ Credenciais reconfiguradas com sucesso!"
    echo ""
    
    echo "🧪 Testando conexão..."
    kubectl cluster-info
    echo ""
    
    echo "📊 Verificando nodes..."
    kubectl get nodes
    echo ""
    
    echo "🏃 Verificando pods..."
    kubectl get pods -o wide
    echo ""
    
    echo "✅ Conexão restaurada! Tentando aplicar deployment..."
    kubectl apply -f k8s/deployment.yaml
    
else
    echo "❌ Falha ao obter credenciais. Possíveis soluções:"
    echo ""
    echo "1. 🔑 Verificar se está autenticado:"
    echo "   gcloud auth login"
    echo ""
    echo "2. 🎯 Definir projeto correto:"
    echo "   gcloud config set project $PROJECT_ID"
    echo ""
    echo "3. 🏗️  Verificar se o cluster existe na zona correta:"
    echo "   gcloud container clusters list"
    echo ""
    echo "4. 🌐 Verificar conectividade de rede:"
    echo "   ping 8.8.8.8"
    echo ""
    echo "5. 🔄 Recriar o cluster se necessário:"
    echo "   ./setup-gke.sh"
fi

echo ""
echo "🔍 Informações de debug:"
echo "Cluster: $CLUSTER_NAME"
echo "Zone: $ZONE"
echo "Project: $PROJECT_ID"
echo "Endpoint atual: $(kubectl config view --minify -o jsonpath='{.clusters[0].cluster.server}' 2>/dev/null || echo 'N/A')"

#!/bin/bash

echo "⚡ Deploy simples sem validação..."
echo ""

# Aplicar deployment sem validação OpenAPI
echo "🚀 Aplicando deployment..."
kubectl apply -f k8s/deployment.yaml --validate=false --server-side=true

echo ""
echo "📊 Status:"
kubectl get all -l app=authentication-service

echo ""
echo "💡 Comandos úteis:"
echo "Ver pods: kubectl get pods"
echo "Ver logs: kubectl logs -l app=authentication-service"
echo "Port forward: kubectl port-forward service/authentication-service 8080:80"

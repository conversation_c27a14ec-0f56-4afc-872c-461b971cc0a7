#!/bin/bash

echo "🔍 Diagnosticando problemas de scheduling..."
echo ""

echo "📊 Status dos Pods:"
kubectl get pods -o wide
echo ""

echo "⚠️  Pods com problemas:"
kubectl get pods --field-selector=status.phase!=Running
echo ""

echo "📋 Eventos detalhados (últimos 20):"
kubectl get events --sort-by='.lastTimestamp' | tail -20
echo ""

echo "🏗️  Status dos Nodes:"
kubectl get nodes -o wide
echo ""

echo "💾 Capacidade dos Nodes:"
kubectl describe nodes | grep -E "(Name:|Capacity:|Allocatable:|Allocated resources:)" -A 3
echo ""

echo "🔧 Node Pool Status (GKE):"
gcloud container node-pools list --cluster=cluster-high-capital-dev --zone=us-east1 2>/dev/null || echo "⚠️  Não foi possível obter info do node pool"
echo ""

echo "📈 Cluster Autoscaler Status:"
kubectl get events --field-selector reason=FailedScheduling | tail -10
echo ""

echo "🚨 Pods pendentes com detalhes:"
kubectl describe pods | grep -A 10 -B 5 "FailedScheduling\|Pending"
echo ""

echo "💡 Soluções possíveis:"
echo "1. Reduzir recursos solicitados nos pods"
echo "2. Aumentar o tamanho máximo do node pool"
echo "3. Usar machine types maiores"
echo "4. Verificar node selectors/taints"
echo ""

echo "🔧 Comandos úteis:"
echo "# Aplicar configuração minimal:"
echo "kubectl apply -f k8s/deployment-minimal.yaml"
echo ""
echo "# Escalar node pool manualmente:"
echo "gcloud container clusters resize cluster-high-capital-dev --num-nodes=2 --zone=us-east1"
echo ""
echo "# Verificar configuração do autoscaler:"
echo "gcloud container node-pools describe default-pool --cluster=cluster-high-capital-dev --zone=us-east1"

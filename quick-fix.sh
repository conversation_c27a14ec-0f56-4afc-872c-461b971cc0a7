#!/bin/bash

echo "⚡ Quick Fix para problemas de conexão com Kubernetes..."
echo ""

# Configurações
PROJECT_ID="highcapital-470117"
CLUSTER_NAME="cluster-high-capital-dev"
ZONE="us-east1-b"

echo "🔧 Executando correções rápidas..."
echo ""

# 1. Verificar e configurar projeto
echo "1️⃣  Configurando projeto..."
gcloud config set project $PROJECT_ID
echo ""

# 2. Reautenticar se necessário
echo "2️⃣  Verificando autenticação..."
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q "@"; then
    echo "🔑 Necessário reautenticar..."
    gcloud auth login
fi
echo ""

# 3. Obter credenciais do cluster
echo "3️⃣  Obtendo credenciais do cluster..."
gcloud container clusters get-credentials $CLUSTER_NAME --zone=$ZONE --project=$PROJECT_ID
echo ""

# 4. Testar conexão
echo "4️⃣  Testando conexão..."
if timeout 15 kubectl cluster-info >/dev/null 2>&1; then
    echo "✅ Conexão OK!"
    
    echo ""
    echo "📊 Status atual:"
    kubectl get nodes
    kubectl get pods -o wide
    
    echo ""
    echo "🚀 Aplicando deployment..."
    kubectl apply -f k8s/deployment.yaml
    
else
    echo "❌ Ainda há problemas de conexão."
    echo ""
    echo "🔍 Verificando se o cluster existe..."
    
    if gcloud container clusters describe $CLUSTER_NAME --zone=$ZONE >/dev/null 2>&1; then
        echo "✅ Cluster existe, mas não consegue conectar."
        echo ""
        echo "💡 Possíveis soluções:"
        echo "1. Verificar firewall/VPN"
        echo "2. Aguardar alguns minutos (cluster pode estar inicializando)"
        echo "3. Tentar zona diferente"
        echo ""
        echo "🔄 Tentando com IP interno..."
        gcloud container clusters get-credentials $CLUSTER_NAME --zone=$ZONE --internal-ip
        
    else
        echo "❌ Cluster não encontrado!"
        echo ""
        echo "🏗️  Criando novo cluster..."
        read -p "Deseja criar um novo cluster? (y/n): " create_cluster
        
        if [ "$create_cluster" = "y" ] || [ "$create_cluster" = "Y" ]; then
            ./setup-gke.sh
        else
            echo "📋 Para criar manualmente:"
            echo "./setup-gke.sh"
        fi
    fi
fi

echo ""
echo "🔍 Informações de debug:"
echo "Project: $(gcloud config get-value project)"
echo "Account: $(gcloud config get-value account)"
echo "Cluster: $CLUSTER_NAME"
echo "Zone: $ZONE"

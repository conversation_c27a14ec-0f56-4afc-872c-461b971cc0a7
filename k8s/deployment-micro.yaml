apiVersion: apps/v1
kind: Deployment
metadata:
  name: authentication-service
  labels:
    app: authentication-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: authentication-service
  template:
    metadata:
      labels:
        app: authentication-service
    spec:
      containers:
      - name: authentication-service
        image: gcr.io/highcapital-470117/authentication-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Production"
        - name: ASPNETCORE_URLS
          value: "http://+:8080"
        - name: DOTNET_SYSTEM_GLOBALIZATION_INVARIANT
          value: "1"
        - name: DOTNET_RUNNING_IN_CONTAINER
          value: "true"
        - name: DOTNET_EnableDiagnostics
          value: "0"
        resources:
          requests:
            memory: "32Mi"
            cpu: "25m"
          limits:
            memory: "64Mi"
            cpu: "50m"
        # Sem health checks para economizar recursos máximos
      # Tolerations para aceitar qualquer node
      tolerations:
      - operator: "Exists"
        effect: "NoSchedule"
      # Node affinity flexível
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 1
            preference:
              matchExpressions:
              - key: node.kubernetes.io/instance-type
                operator: Exists
---
apiVersion: v1
kind: Service
metadata:
  name: authentication-service
  labels:
    app: authentication-service
spec:
  type: ClusterIP  # Mudado para ClusterIP para economizar recursos
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
  selector:
    app: authentication-service
---
# Ingress simples se precisar de acesso externo
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: authentication-service-ingress
  annotations:
    kubernetes.io/ingress.class: "gce"
spec:
  rules:
  - http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: authentication-service
            port:
              number: 80

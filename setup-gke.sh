#!/bin/bash

# Script para configurar o cluster GKE
# Execute este script uma vez para criar o cluster

# Configurações
PROJECT_ID="highcapital-470117"
CLUSTER_NAME="cluster-high-capital-dev"
ZONE="us-east1"
MACHINE_TYPE="e2-medium"
NUM_NODES="2"

# echo "Configurando projeto GCP..."
# gcloud config set project $PROJECT_ID

# echo "Habilitando APIs necessárias..."
# gcloud services enable container.googleapis.com
# gcloud services enable containerregistry.googleapis.com

echo "Criando cluster GKE..."
gcloud container clusters create $CLUSTER_NAME --zone=$ZONE --machine-type=$MACHINE_TYPE --num-nodes=$NUM_NODES --enable-autoscaling --min-nodes=1 --max-nodes=5 --enable-autorepair --enable-autoupgrade

echo "Obtendo credenciais do cluster..."
gcloud container clusters get-credentials $CLUSTER_NAME --zone=$ZONE

echo "Cluster criado com sucesso!"
echo "Para deletar o cluster, execute:"
echo "gcloud container clusters delete $CLUSTER_NAME --zone=$ZONE"

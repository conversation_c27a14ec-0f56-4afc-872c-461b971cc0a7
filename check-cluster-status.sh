#!/bin/bash

echo "🔍 Verificando status do cluster..."
echo ""

PROJECT_ID="highcapital-470117"
CLUSTER_NAME="cluster-high-capital-dev"
ZONE="us-east1-b"

# Função para verificar se comando existe
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

if ! command_exists gcloud; then
    echo "❌ gcloud CLI não encontrado. Instale primeiro."
    exit 1
fi

echo "🎯 Configuração atual:"
echo "Project: $PROJECT_ID"
echo "Cluster: $CLUSTER_NAME"
echo "Zone: $ZONE"
echo ""

echo "🔑 Status da autenticação:"
gcloud auth list --filter=status:ACTIVE --format="table(account,status)"
echo ""

echo "📊 Listando clusters disponíveis:"
gcloud container clusters list --project=$PROJECT_ID
echo ""

echo "🔍 Verificando se o cluster específico existe..."
CLUSTER_EXISTS=$(gcloud container clusters list --filter="name:$CLUSTER_NAME AND location:$ZONE" --format="value(name)" 2>/dev/null)

if [ -z "$CLUSTER_EXISTS" ]; then
    echo "❌ Cluster '$CLUSTER_NAME' não encontrado na zona '$ZONE'"
    echo ""
    echo "💡 Opções disponíveis:"
    echo "1. Criar novo cluster"
    echo "2. Verificar outras zonas"
    echo "3. Listar todos os clusters"
    echo ""
    
    read -p "Escolha uma opção (1-3): " choice
    
    case $choice in
        1)
            echo "🏗️  Criando novo cluster..."
            ./setup-gke.sh
            ;;
        2)
            echo "🌍 Clusters em todas as zonas:"
            gcloud container clusters list --project=$PROJECT_ID
            ;;
        3)
            echo "📋 Todos os clusters:"
            gcloud container clusters list --project=$PROJECT_ID --format="table(name,location,status,currentNodeCount)"
            ;;
        *)
            echo "❌ Opção inválida"
            ;;
    esac
else
    echo "✅ Cluster encontrado!"
    
    echo "📊 Detalhes do cluster:"
    gcloud container clusters describe $CLUSTER_NAME --zone=$ZONE --format="table(name,status,currentNodeCount,location,endpoint)"
    echo ""
    
    echo "🔄 Obtendo credenciais..."
    gcloud container clusters get-credentials $CLUSTER_NAME --zone=$ZONE --project=$PROJECT_ID
    
    if [ $? -eq 0 ]; then
        echo "✅ Credenciais obtidas com sucesso!"
        echo ""
        
        echo "🧪 Testando conectividade..."
        timeout 10 kubectl cluster-info
        
        if [ $? -eq 0 ]; then
            echo "✅ Conexão OK!"
            echo ""
            echo "📊 Status dos nodes:"
            kubectl get nodes
            echo ""
            echo "🏃 Status dos pods:"
            kubectl get pods -o wide
        else
            echo "❌ Timeout na conexão. Possíveis causas:"
            echo "1. Firewall bloqueando conexão"
            echo "2. Cluster em estado não saudável"
            echo "3. Problemas de rede"
            echo ""
            echo "🔧 Tentando recriar credenciais..."
            gcloud container clusters get-credentials $CLUSTER_NAME --zone=$ZONE --project=$PROJECT_ID --internal-ip
        fi
    else
        echo "❌ Falha ao obter credenciais"
    fi
fi

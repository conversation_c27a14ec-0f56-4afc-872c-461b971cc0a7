#!/bin/bash

echo "🔍 Verificando recursos do cluster..."
echo ""

echo "📊 Nodes disponíveis:"
kubectl get nodes -o wide
echo ""

echo "💾 Recursos por node:"
kubectl describe nodes | grep -A 5 "Allocated resources"
echo ""

echo "📈 Uso atual de recursos:"
kubectl top nodes 2>/dev/null || echo "⚠️  Metrics server não disponível"
echo ""

echo "🏃 Pods em execução:"
kubectl get pods -o wide
echo ""

echo "📋 Eventos recentes:"
kubectl get events --sort-by='.lastTimestamp' | tail -10
echo ""

echo "💡 Dicas para resolver problemas de memória:"
echo "1. Use deployment-minimal.yaml para recursos muito limitados"
echo "2. Considere usar nodes com mais memória (e2-standard-2 ou maior)"
echo "3. Verifique se há outros pods consumindo recursos"
echo ""

echo "🔧 Para aplicar configuração minimal:"
echo "kubectl apply -f k8s/deployment-minimal.yaml"

#!/bin/bash

echo "🚀 Aplicando deployment com correções para problemas de conectividade..."
echo ""

# Configurações
PROJECT_ID="highcapital-470117"
CLUSTER_NAME="cluster-high-capital-dev"
ZONE="us-east1-b"

echo "🔧 Tentativa 1: Aplicar com validação desabilitada..."
kubectl apply -f k8s/deployment.yaml --validate=false

if [ $? -eq 0 ]; then
    echo "✅ Deployment aplicado com sucesso!"
    echo ""
    
    echo "⏳ Aguardando pods ficarem prontos..."
    sleep 30
    
    echo "📊 Status dos pods:"
    kubectl get pods -o wide
    
    echo ""
    echo "📋 Status do deployment:"
    kubectl get deployment authentication-service-deployment
    
    echo ""
    echo "🌐 Status do service:"
    kubectl get service authentication-service
    
else
    echo "❌ Falha na primeira tentativa. Tentando soluções alternativas..."
    echo ""
    
    echo "🔧 Tentativa 2: Reconfigurar credenciais..."
    gcloud container clusters get-credentials $CLUSTER_NAME --zone=$ZONE --project=$PROJECT_ID
    
    echo ""
    echo "🔧 Tentativa 3: Aplicar novamente..."
    kubectl apply -f k8s/deployment.yaml --validate=false
    
    if [ $? -eq 0 ]; then
        echo "✅ Sucesso na segunda tentativa!"
    else
        echo "❌ Ainda há problemas. Tentando abordagem alternativa..."
        echo ""
        
        echo "🔧 Tentativa 4: Usar servidor de API público..."
        gcloud container clusters get-credentials $CLUSTER_NAME --zone=$ZONE --project=$PROJECT_ID --no-enable-ip-alias
        
        echo ""
        echo "🔧 Tentativa 5: Aplicar com timeout maior..."
        kubectl apply -f k8s/deployment.yaml --validate=false --timeout=60s
        
        if [ $? -ne 0 ]; then
            echo "❌ Todas as tentativas falharam."
            echo ""
            echo "💡 Soluções manuais:"
            echo "1. Verificar se o cluster está ativo:"
            echo "   gcloud container clusters describe $CLUSTER_NAME --zone=$ZONE"
            echo ""
            echo "2. Tentar criar um novo cluster:"
            echo "   ./setup-gke.sh"
            echo ""
            echo "3. Aplicar recursos individualmente:"
            echo "   kubectl create deployment authentication-service-deployment --image=gcr.io/$PROJECT_ID/authentication-service:latest --validate=false"
            echo "   kubectl expose deployment authentication-service-deployment --port=80 --target-port=8080 --type=LoadBalancer --validate=false"
            echo ""
            echo "4. Verificar conectividade de rede:"
            echo "   ping 8.8.8.8"
            exit 1
        fi
    fi
fi

echo ""
echo "🎯 Verificação final:"
echo "Pods:"
kubectl get pods
echo ""
echo "Services:"
kubectl get services
echo ""
echo "Deployments:"
kubectl get deployments
echo ""

echo "💡 Para monitorar o progresso:"
echo "kubectl get pods -w"
echo ""
echo "💡 Para ver logs:"
echo "kubectl logs -l app=authentication-service"
echo ""
echo "💡 Para acessar o serviço:"
echo "kubectl port-forward service/authentication-service 8080:80"

#!/bin/bash

echo "🔧 Tentando resolver problemas de scheduling..."
echo ""

# Função para verificar se comando existe
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 1. Limpar pods com falha
echo "🧹 Limpando pods com falha..."
kubectl delete pods --field-selector=status.phase=Failed 2>/dev/null || true
kubectl delete pods --field-selector=status.phase=Pending --grace-period=0 --force 2>/dev/null || true
echo ""

# 2. Aplicar configuração micro (recursos mínimos)
echo "📦 Aplicando configuração com recursos mínimos..."
kubectl apply -f k8s/deployment-micro.yaml
echo ""

# 3. Verificar se precisa escalar o cluster
echo "📊 Verificando necessidade de escalar cluster..."
PENDING_PODS=$(kubectl get pods --field-selector=status.phase=Pending --no-headers 2>/dev/null | wc -l)
if [ "$PENDING_PODS" -gt 0 ]; then
    echo "⚠️  Encontrados $PENDING_PODS pods pendentes"
    
    if command_exists gcloud; then
        echo "🔄 Tentando escalar o node pool..."
        gcloud container clusters resize high-capital-cluster-dev \
            --num-nodes=2 \
            --zone=us-east1 \
            --quiet 2>/dev/null || echo "❌ Falha ao escalar cluster"
    else
        echo "⚠️  gcloud CLI não encontrado. Escale manualmente:"
        echo "gcloud container clusters resize high-capital-cluster-dev --num-nodes=2 --zone=us-east1"
    fi
fi
echo ""

# 4. Aguardar um pouco e verificar status
echo "⏳ Aguardando 30 segundos para verificar status..."
sleep 30

echo "📊 Status atual dos pods:"
kubectl get pods -o wide
echo ""

echo "🎯 Status dos nodes:"
kubectl get nodes
echo ""

# 5. Verificar se ainda há problemas
FAILED_PODS=$(kubectl get pods --field-selector=status.phase=Failed --no-headers 2>/dev/null | wc -l)
PENDING_PODS=$(kubectl get pods --field-selector=status.phase=Pending --no-headers 2>/dev/null | wc -l)

if [ "$FAILED_PODS" -gt 0 ] || [ "$PENDING_PODS" -gt 0 ]; then
    echo "⚠️  Ainda há problemas. Executando diagnóstico..."
    ./diagnose-scheduling.sh
else
    echo "✅ Problemas de scheduling resolvidos!"
    echo ""
    echo "🌐 Para acessar o serviço:"
    echo "kubectl port-forward service/authentication-service 8080:80"
    echo "Depois acesse: http://localhost:8080"
fi

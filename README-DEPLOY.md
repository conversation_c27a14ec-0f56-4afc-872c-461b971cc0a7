# Deploy no Google Kubernetes Engine (GKE)

Este guia mostra como fazer deploy da aplicação Authentication Service no GKE de forma simples.

## Pré-requisitos

1. Conta no Google Cloud Platform
2. `gcloud` CLI instalado e configurado
3. `kubectl` instalado
4. Docker instalado (para testes locais)

## Configuração Inicial

### 1. Configurar Secrets no GitHub

No seu repositório GitHub, vá em Settings > Secrets and variables > Actions e adicione:

- `GCP_PROJECT_ID`: `highcapital-470117`
- `GCP_SA_KEY`: JSON da service account com permissões para GKE e Container Registry
- `GITHUB_TOKEN`: Token do GitHub com acesso ao GitHub Packages (já disponível automaticamente)
- `GITHUB_USERNAME`: `aryelzx` (usuário para acessar GitHub Packages)

### 2. Criar Service Account no GCP

```bash
# Criar service account
gcloud iam service-accounts create github-actions \
    --description="Service account for GitHub Actions" \
    --display-name="GitHub Actions"

# Adicionar roles necessárias
gcloud projects add-iam-policy-binding PROJECT_ID \
    --member="serviceAccount:github-actions@PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/container.developer"

gcloud projects add-iam-policy-binding PROJECT_ID \
    --member="serviceAccount:github-actions@PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/storage.admin"

# Gerar chave JSON
gcloud iam service-accounts keys create key.json \
    --iam-account=github-actions@PROJECT_ID.iam.gserviceaccount.com
```

### 3. Criar Cluster GKE

```bash
# Editar o arquivo setup-gke.sh com seu PROJECT_ID
# Depois executar:
./setup-gke.sh
```

## Deploy

### Deploy Automático

- Faça push para a branch `main`
- O GitHub Actions irá automaticamente fazer o build e deploy

### Deploy Manual

```bash
# Build da imagem (com credenciais do GitHub)
docker build -t gcr.io/highcapital-470117/authentication-service:latest \
  --build-arg GITHUB_TOKEN="seu_github_token" \
  --build-arg GITHUB_USERNAME="aryelzx" .

# Push para Container Registry
docker push gcr.io/highcapital-470117/authentication-service:latest

# Deploy no Kubernetes
kubectl apply -f k8s/deployment.yaml
```

## Verificar Deploy

```bash
# Ver pods
kubectl get pods

# Ver services
kubectl get services

# Ver logs
kubectl logs -l app=authentication-service

# Obter IP externo
kubectl get service authentication-service
```

## Limpeza

```bash
# Deletar deployment
kubectl delete -f k8s/deployment.yaml

# Deletar cluster (cuidado!)
gcloud container clusters delete cluster-high-capital-dev --zone=us-east1
```

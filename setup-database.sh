#!/bin/bash

echo "🗄️  Configurando banco de dados..."
echo ""

# Verificar se o dotnet ef está instalado
if ! command -v dotnet-ef &> /dev/null; then
    echo "📦 Instalando Entity Framework CLI..."
    dotnet tool install --global dotnet-ef
fi

echo "🔄 Criando migration para Identity..."
cd src/Infrastructure

# Criar migration para Identity
dotnet ef migrations add InitialIdentityMigration \
    --context ApplicationDbContext \
    --startup-project ../Api \
    --output-dir Data/Migrations

if [ $? -eq 0 ]; then
    echo "✅ Migration criada com sucesso!"
    
    echo "🚀 Aplicando migration ao banco de dados..."
    dotnet ef database update \
        --context ApplicationDbContext \
        --startup-project ../Api
    
    if [ $? -eq 0 ]; then
        echo "✅ Banco de dados configurado com sucesso!"
    else
        echo "❌ Erro ao aplicar migration"
    fi
else
    echo "❌ Erro ao criar migration"
fi

cd ../..
echo ""
echo "💡 Para testar a aplicação:"
echo "./test-app.sh"
